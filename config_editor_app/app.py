import glob
import json
import sys
from pathlib import Path

import gradio as gr

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ConfigEditor:
    def __init__(self):
        self.project_root = project_root
        self.config_dir = (
            self.project_root / "dc_ai_red_line_review_langextract" / "config"
        )
        self.data_dir = self.project_root / "data" / "individual_cases"
        self.main_script = (
            self.project_root / "dc_ai_red_line_review_langextract" / "main_pipe.py"
        )

        # 配置文件路径
        self.config_files = {
            "examples": self.config_dir / "examples.json",
            "keywords": self.config_dir / "keywords.json",
            "prompts": self.config_dir / "prompts.json",
        }

    def load_config_file(self, config_type: str) -> str:
        """加载配置文件内容"""
        try:
            with open(self.config_files[config_type], encoding="utf-8") as f:
                content = json.load(f)
                return json.dumps(content, indent=2, ensure_ascii=False)
        except Exception as e:
            return f"Error loading {config_type}.json: {str(e)}"

    def save_config_file(self, config_type: str, content: str) -> str:
        """保存配置文件内容"""
        try:
            # 验证JSON格式
            json_data = json.loads(content)

            # 保存文件
            with open(self.config_files[config_type], "w", encoding="utf-8") as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)

            return f"✅ {config_type}.json 保存成功!"
        except json.JSONDecodeError as e:
            return f"❌ JSON格式错误: {str(e)}"
        except Exception as e:
            return f"❌ 保存失败: {str(e)}"

    def get_case_list(self) -> list[str]:
        """获取所有case文件列表"""
        try:
            case_files = glob.glob(str(self.data_dir / "case_*.json"))
            case_ids = [Path(f).stem.replace("case_", "") for f in case_files]
            return sorted(case_ids)
        except Exception as e:
            return [f"Error loading cases: {str(e)}"]

    def load_case_data(self, case_id: str) -> str:
        """加载指定case的数据"""
        try:
            case_file = self.data_dir / f"case_{case_id}.json"
            with open(case_file, encoding="utf-8") as f:
                content = json.load(f)
                return json.dumps(content, indent=2, ensure_ascii=False)
        except Exception as e:
            return f"Error loading case {case_id}: {str(e)}"

    def run_main_pipe(self, case_id: str) -> tuple[str, str]:
        """运行main_pipe.py处理指定case"""
        try:
            # 加载case数据
            case_file = self.data_dir / f"case_{case_id}.json"
            with open(case_file, encoding="utf-8") as f:
                case_data = json.load(f)

            # 导入并运行pipeline
            sys.path.insert(0, str(self.project_root))
            from dc_ai_red_line_review_langextract.main_pipe import BasicPipeline

            # 创建pipeline实例
            pipeline = BasicPipeline()

            # 运行处理
            result = pipeline.run(messages=case_data["messages"], caseId=case_id)

            # 格式化输出
            output = json.dumps(result, indent=2, ensure_ascii=False)
            status = f"✅ 处理完成 (Case: {case_id})"

            return status, output

        except FileNotFoundError:
            return f"❌ Case文件不存在: {case_id}", ""
        except Exception as e:
            import traceback

            error_detail = traceback.format_exc()
            return f"❌ 运行错误: {str(e)}", f"Error details:\n{error_detail}"


def create_interface():
    """创建Gradio界面"""
    editor = ConfigEditor()

    with gr.Blocks(title="红线审查配置编辑器", theme=gr.themes.Soft()) as app:
        gr.Markdown("# 🔧 红线审查配置编辑器")
        gr.Markdown("用于编辑配置文件并测试红线审查系统")

        with gr.Tabs():
            # 配置文件编辑标签页
            with gr.TabItem("📝 配置文件编辑"):
                with gr.Row():
                    with gr.Column(scale=1):
                        config_type = gr.Dropdown(
                            choices=["examples", "keywords", "prompts"],
                            value="examples",
                            label="选择配置文件",
                        )
                        load_btn = gr.Button("🔄 加载配置", variant="secondary")
                        save_btn = gr.Button("💾 保存配置", variant="primary")
                        save_status = gr.Textbox(label="保存状态", interactive=False)

                    with gr.Column(scale=3):
                        config_content = gr.Code(
                            language="json", label="配置文件内容", lines=25
                        )

                # 绑定事件
                load_btn.click(
                    fn=editor.load_config_file,
                    inputs=[config_type],
                    outputs=[config_content],
                )

                save_btn.click(
                    fn=editor.save_config_file,
                    inputs=[config_type, config_content],
                    outputs=[save_status],
                )

                # 自动加载默认配置
                config_type.change(
                    fn=editor.load_config_file,
                    inputs=[config_type],
                    outputs=[config_content],
                )

            # 测试运行标签页
            with gr.TabItem("🚀 测试运行"):
                with gr.Row():
                    with gr.Column(scale=1):
                        case_dropdown = gr.Dropdown(
                            choices=editor.get_case_list(),
                            label="选择Case ID",
                            value=editor.get_case_list()[0]
                            if editor.get_case_list()
                            else None,
                        )
                        refresh_cases_btn = gr.Button(
                            "🔄 刷新Case列表", variant="secondary"
                        )
                        run_btn = gr.Button("▶️ 运行处理", variant="primary")
                        run_status = gr.Textbox(label="运行状态", interactive=False)

                    with gr.Column(scale=2):
                        case_content = gr.Code(
                            language="json", label="Case数据预览", lines=15
                        )

                with gr.Row():
                    run_output = gr.Textbox(
                        label="运行输出", lines=20, max_lines=30, show_copy_button=True
                    )

                # 绑定事件
                refresh_cases_btn.click(
                    fn=lambda: gr.Dropdown(choices=editor.get_case_list()),
                    outputs=[case_dropdown],
                )

                case_dropdown.change(
                    fn=editor.load_case_data,
                    inputs=[case_dropdown],
                    outputs=[case_content],
                )

                run_btn.click(
                    fn=editor.run_main_pipe,
                    inputs=[case_dropdown],
                    outputs=[run_status, run_output],
                )

                # 自动加载默认case数据
                if editor.get_case_list():
                    app.load(
                        fn=editor.load_case_data,
                        inputs=[case_dropdown],
                        outputs=[case_content],
                    )

            # 帮助标签页
            with gr.TabItem("❓ 使用帮助"):
                gr.Markdown("""
                ## 使用说明
                
                ### 📝 配置文件编辑
                1. **选择配置文件**: 从下拉菜单中选择要编辑的配置文件
                2. **编辑内容**: 在代码编辑器中修改JSON配置
                3. **保存配置**: 点击保存按钮保存修改
                
                ### 🚀 测试运行
                1. **选择Case**: 从下拉菜单中选择要测试的Case ID
                2. **预览数据**: 查看选中Case的原始数据
                3. **运行处理**: 点击运行按钮执行main_pipe.py
                4. **查看结果**: 在输出区域查看处理结果
                
                ### 📋 配置文件说明
                - **examples.json**: 训练示例配置，包含文本和提取结果的示例
                - **keywords.json**: 关键词配置，包含各类关键词列表
                - **prompts.json**: 提示词配置，包含AI模型使用的提示词模板
                
                ### 💡 优化建议
                1. 根据运行结果调整配置文件
                2. 添加新的关键词或示例
                3. 优化提示词以提高准确性
                4. 测试多个不同的Case以验证效果
                """)

    return app


if __name__ == "__main__":
    app = create_interface()
    app.launch(server_name="0.0.0.0", server_port=7860, share=False, debug=True)
