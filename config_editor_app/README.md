# 🔧 红线审查配置编辑器

一个基于Gradio的Web界面，用于编辑红线审查系统的配置文件并进行测试。

## 功能特性

- 📝 **配置文件编辑**: 可视化编辑examples.json、keywords.json、prompts.json
- 🚀 **实时测试**: 选择Case ID运行main_pipe.py进行测试
- 📊 **结果展示**: 实时查看处理结果和输出
- 🔄 **配置优化**: 根据测试结果优化配置参数

## 快速开始

### 方法1: 使用启动脚本（推荐）

```bash
cd config_editor_app
./run.sh
```

### 方法2: 手动启动

```bash
cd config_editor_app

# 安装依赖
pip install -r requirements.txt

# 启动应用
python app.py
```

启动后访问: http://localhost:7860

## 界面说明

### 📝 配置文件编辑

1. **选择配置文件**: 从下拉菜单选择要编辑的配置文件
2. **编辑内容**: 在JSON编辑器中修改配置
3. **保存配置**: 点击保存按钮保存修改

### 🚀 测试运行

1. **选择Case**: 从下拉菜单选择要测试的Case ID
2. **预览数据**: 查看选中Case的原始数据
3. **运行处理**: 点击运行按钮执行处理
4. **查看结果**: 在输出区域查看处理结果

## 配置文件说明

### examples.json
训练示例配置，包含：
- 对话文本示例
- 提取结果示例
- 分类标签示例

### keywords.json
关键词配置，包含：
- 关键联系人信息
- 政府机构查询关键词
- 内部系统关键词

### prompts.json
提示词配置，包含：
- AI模型提示词模板
- 提取类别定义
- 分类描述

## 优化工作流

1. **选择测试Case** → 查看原始数据
2. **运行处理** → 查看当前结果
3. **分析结果** → 识别需要改进的地方
4. **编辑配置** → 添加关键词、示例或优化提示词
5. **重新测试** → 验证改进效果
6. **重复优化** → 直到达到满意效果

## 注意事项

- 确保所有配置文件的JSON格式正确
- 修改配置后建议多测试几个不同的Case
- 保存配置前请仔细检查语法
- 如遇到错误，查看详细错误信息进行调试

## 技术栈

- **前端**: Gradio
- **后端**: Python
- **配置**: JSON
- **处理引擎**: dc_ai_red_line_review_langextract
