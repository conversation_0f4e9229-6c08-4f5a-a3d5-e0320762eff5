#!/usr/bin/env python3
"""
Script to process ground truth data and prepare it for evaluation.
This script only processes data without running model predictions.
"""

import pandas as pd
import json
import re
from pathlib import Path
from collections import defaultdict

def clean_html_content(content):
    """Extract clean text from HTML/JSON content."""
    if not content:
        return ""
    
    # If it's JSON, try to parse it
    if content.strip().startswith('{'):
        try:
            data = json.loads(content)
            if 'content' in data:
                content = data['content']
        except:
            pass
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', ' ', content)
    # Decode HTML entities
    content = content.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    content = content.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    # Clean up whitespace
    content = re.sub(r'\s+', ' ', content).strip()
    
    return content

def load_and_process_gt_files():
    """Load all GT Excel files and extract annotated data."""
    gt_dir = Path("data/gt")
    excel_files = list(gt_dir.glob("gt_*.xlsx"))
    
    all_annotations = []
    stats = {
        'files_processed': 0,
        'total_rows': 0,
        'annotated_rows': 0,
        'by_file': {}
    }
    
    for excel_file in excel_files:
        print(f"Processing {excel_file.name}...")
        df = pd.read_excel(excel_file)
        
        # Count total rows
        total_rows = len(df)
        stats['total_rows'] += total_rows
        
        # Filter rows with human annotations
        annotated_rows = df[df['人工审核结果'].notna() | df['正确结果'].notna()]
        annotated_count = len(annotated_rows)
        stats['annotated_rows'] += annotated_count
        
        stats['by_file'][excel_file.name] = {
            'total_rows': total_rows,
            'annotated_rows': annotated_count,
            'annotation_rate': annotated_count / total_rows if total_rows > 0 else 0
        }
        
        for _, row in annotated_rows.iterrows():
            annotation = {
                'file_source': excel_file.name,
                'case_id': row['f_case_id'],
                'msg_id': row['f_msg_id'],
                'user_type': row['f_user_type'],
                'original_msg': row['f_original_msg'],
                'keywords': row['f_keywords'],
                'f_type': row['f_type'],
                'f_sub_type': row['f_sub_type'],
                'human_review': row['人工审核结果'] if pd.notna(row['人工审核结果']) else None,
                'needs_optimization': row['是否需要优化模型'] if pd.notna(row['是否需要优化模型']) else None,
                'correct_result': row['正确结果'] if pd.notna(row['正确结果']) else None,
            }
            all_annotations.append(annotation)
        
        stats['files_processed'] += 1
    
    print(f"\n=== Processing Statistics ===")
    print(f"Files processed: {stats['files_processed']}")
    print(f"Total rows: {stats['total_rows']}")
    print(f"Annotated rows: {stats['annotated_rows']}")
    print(f"Overall annotation rate: {stats['annotated_rows']/stats['total_rows']:.2%}")
    
    print(f"\n=== By File ===")
    for filename, file_stats in stats['by_file'].items():
        print(f"{filename}: {file_stats['annotated_rows']}/{file_stats['total_rows']} ({file_stats['annotation_rate']:.2%})")
    
    return all_annotations, stats

def analyze_annotations(annotations):
    """Analyze the annotation patterns."""
    analysis = {
        'total_annotations': len(annotations),
        'by_user_type': defaultdict(int),
        'by_type': defaultdict(int),
        'by_sub_type': defaultdict(int),
        'human_review_categories': defaultdict(int),
        'correct_result_categories': defaultdict(int),
        'needs_optimization': defaultdict(int),
        'case_ids': set(),
        'msg_ids': set()
    }
    
    for ann in annotations:
        analysis['by_user_type'][ann['user_type']] += 1
        analysis['by_type'][ann['f_type']] += 1
        analysis['by_sub_type'][ann['f_sub_type']] += 1
        analysis['case_ids'].add(ann['case_id'])
        analysis['msg_ids'].add(ann['msg_id'])
        
        if ann['human_review']:
            analysis['human_review_categories'][ann['human_review']] += 1
        
        if ann['correct_result']:
            analysis['correct_result_categories'][ann['correct_result']] += 1
            
        if ann['needs_optimization']:
            analysis['needs_optimization'][ann['needs_optimization']] += 1
    
    # Convert sets to counts
    analysis['unique_cases'] = len(analysis['case_ids'])
    analysis['unique_messages'] = len(analysis['msg_ids'])
    analysis['case_ids'] = list(analysis['case_ids'])
    analysis['msg_ids'] = list(analysis['msg_ids'])
    
    return analysis

def build_conversation_data(annotations):
    """Build conversation data from annotations."""
    # Group by case_id
    case_groups = defaultdict(list)
    for ann in annotations:
        case_groups[ann['case_id']].append(ann)
    
    conversations = []
    
    for case_id, msgs in case_groups.items():
        # Sort by msg_id to maintain order
        msgs.sort(key=lambda x: x['msg_id'])
        
        # Build messages list
        messages = []
        for i, msg in enumerate(msgs):
            # Clean the message content
            clean_content = clean_html_content(msg['original_msg'])
            if not clean_content:
                clean_content = msg['keywords']  # Fallback to keywords
            
            messages.append({
                'id': msg['msg_id'],
                'type': msg['user_type'],
                'msg': clean_content
            })
        
        # Get ground truth labels (use the first annotation with labels)
        ground_truth = None
        for msg in msgs:
            if msg['correct_result'] or msg['human_review']:
                ground_truth = {
                    'human_review': msg['human_review'],
                    'correct_result': msg['correct_result'],
                    'needs_optimization': msg['needs_optimization'],
                    'original_type': msg['f_type'],
                    'original_sub_type': msg['f_sub_type']
                }
                break
        
        conversations.append({
            'case_id': case_id,
            'messages': messages,
            'ground_truth': ground_truth,
            'source_files': list(set(msg['file_source'] for msg in msgs)),
            'message_count': len(messages)
        })
    
    return conversations

def save_processed_data(annotations, analysis, conversations, output_dir="data/processed_gt"):
    """Save processed data to files."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save raw annotations
    with open(output_path / "annotations.json", 'w', encoding='utf-8') as f:
        json.dump(annotations, f, indent=2, ensure_ascii=False)
    
    # Save analysis
    # Convert defaultdicts to regular dicts for JSON serialization
    analysis_json = {}
    for key, value in analysis.items():
        if isinstance(value, defaultdict):
            analysis_json[key] = dict(value)
        else:
            analysis_json[key] = value
    
    with open(output_path / "analysis.json", 'w', encoding='utf-8') as f:
        json.dump(analysis_json, f, indent=2, ensure_ascii=False)
    
    # Save conversations
    with open(output_path / "conversations.json", 'w', encoding='utf-8') as f:
        json.dump(conversations, f, indent=2, ensure_ascii=False)
    
    # Save summary report
    with open(output_path / "data_summary.md", 'w', encoding='utf-8') as f:
        f.write("# Ground Truth Data Processing Summary\n\n")
        
        f.write("## Overview\n")
        f.write(f"- Total annotations: {analysis['total_annotations']}\n")
        f.write(f"- Unique cases: {analysis['unique_cases']}\n")
        f.write(f"- Unique messages: {analysis['unique_messages']}\n")
        f.write(f"- Conversations built: {len(conversations)}\n\n")
        
        f.write("## User Type Distribution\n")
        for user_type, count in analysis['by_user_type'].items():
            f.write(f"- {user_type}: {count}\n")
        f.write("\n")
        
        f.write("## Type Distribution\n")
        for type_name, count in analysis['by_type'].items():
            f.write(f"- {type_name}: {count}\n")
        f.write("\n")
        
        f.write("## Sub-Type Distribution\n")
        for sub_type, count in analysis['by_sub_type'].items():
            f.write(f"- {sub_type}: {count}\n")
        f.write("\n")
        
        f.write("## Human Review Categories\n")
        for category, count in sorted(analysis['human_review_categories'].items(), key=lambda x: x[1], reverse=True):
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Correct Result Categories\n")
        for category, count in sorted(analysis['correct_result_categories'].items(), key=lambda x: x[1], reverse=True):
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Needs Optimization\n")
        for category, count in analysis['needs_optimization'].items():
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Sample Conversations\n")
        for i, conv in enumerate(conversations[:5]):  # Show first 5 conversations
            f.write(f"\n### Case {conv['case_id']}\n")
            f.write(f"- Messages: {conv['message_count']}\n")
            f.write(f"- Source files: {conv['source_files']}\n")
            if conv['ground_truth']:
                gt = conv['ground_truth']
                f.write(f"- Human review: {gt['human_review']}\n")
                f.write(f"- Correct result: {gt['correct_result']}\n")
                f.write(f"- Needs optimization: {gt['needs_optimization']}\n")
            f.write(f"- Sample message: {conv['messages'][0]['msg'][:200]}...\n")
    
    print(f"Processed data saved to {output_path}")
    return output_path

def main():
    """Main function."""
    print("Starting ground truth data processing...")
    
    # Step 1: Load and process GT files
    annotations, stats = load_and_process_gt_files()
    
    # Step 2: Analyze annotations
    print("\nAnalyzing annotations...")
    analysis = analyze_annotations(annotations)
    
    # Step 3: Build conversation data
    print("\nBuilding conversation data...")
    conversations = build_conversation_data(annotations)
    
    print(f"Built {len(conversations)} conversations from {len(annotations)} annotations")
    
    # Step 4: Save processed data
    output_path = save_processed_data(annotations, analysis, conversations)
    
    # Print summary
    print(f"\n=== PROCESSING SUMMARY ===")
    print(f"Total annotations: {analysis['total_annotations']}")
    print(f"Unique cases: {analysis['unique_cases']}")
    print(f"Unique messages: {analysis['unique_messages']}")
    print(f"Conversations built: {len(conversations)}")
    print(f"Data saved to: {output_path}")
    
    # Show some sample data
    print(f"\n=== SAMPLE HUMAN REVIEW CATEGORIES ===")
    for category, count in sorted(analysis['human_review_categories'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"{category}: {count}")
    
    print(f"\n=== SAMPLE CORRECT RESULT CATEGORIES ===")
    for category, count in sorted(analysis['correct_result_categories'].items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"{category}: {count}")

if __name__ == "__main__":
    main()
