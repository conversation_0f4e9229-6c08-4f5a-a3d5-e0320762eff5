#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to explore the ground truth Excel files and understand their structure.
"""

import pandas as pd
import json
from pathlib import Path

def explore_excel_file(file_path):
    """Explore the structure of an Excel file."""
    print(f"\n=== Exploring {file_path} ===")
    
    try:
        # Read the Excel file
        df = pd.read_excel(file_path)
        
        print(f"Shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("\nFirst few rows:")
        print(df.head())
        
        print("\nColumn info:")
        print(df.info())
        
        print("\nSample data types:")
        for col in df.columns:
            print(f"{col}: {df[col].dtype}")
            if df[col].dtype == 'object':
                print(f"  Sample values: {df[col].dropna().head(3).tolist()}")
        
        return df
        
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def main():
    """Main function to explore all GT files."""
    gt_dir = Path("data/gt")
    
    # Find all Excel files
    excel_files = list(gt_dir.glob("gt_*.xlsx"))
    print(f"Found {len(excel_files)} Excel files: {[f.name for f in excel_files]}")
    
    all_dfs = {}
    
    for excel_file in excel_files:
        df = explore_excel_file(excel_file)
        if df is not None:
            all_dfs[excel_file.name] = df
    
    # Compare structures across files
    if len(all_dfs) > 1:
        print("\n=== Comparing file structures ===")
        first_file = list(all_dfs.keys())[0]
        first_cols = set(all_dfs[first_file].columns)
        
        for filename, df in all_dfs.items():
            current_cols = set(df.columns)
            if current_cols != first_cols:
                print(f"{filename} has different columns:")
                print(f"  Missing: {first_cols - current_cols}")
                print(f"  Extra: {current_cols - first_cols}")
            else:
                print(f"{filename} has same column structure as {first_file}")
    
    # Look for case_id or similar identifier columns
    print("\n=== Looking for case identifiers ===")
    for filename, df in all_dfs.items():
        print(f"\n{filename}:")
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['case', 'id', 'ticket', 'number']):
                print(f"  Potential ID column: {col}")
                print(f"    Sample values: {df[col].dropna().head(5).tolist()}")

if __name__ == "__main__":
    main()
