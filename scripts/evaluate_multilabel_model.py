#!/usr/bin/env python3
"""
Multi-label evaluation for the red line review model.
Evaluates each category separately and provides comprehensive metrics.
"""

import json
import sys
import os
from pathlib import Path
from collections import defaultdict
import numpy as np
from sklearn.metrics import (
    precision_score,
    recall_score,
    f1_score,
    accuracy_score,
    classification_report,
    confusion_matrix,
    multilabel_confusion_matrix,
    hamming_loss,
    jaccard_score,
)

# Add your model path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def load_multilabel_ground_truth(limit=None):
    """Load the multi-label ground truth dataset."""
    conversation_file = Path("data/multilabel_ground_truth/conversation_data.json")
    labels_file = Path("data/multilabel_ground_truth/multilabel_labels.json")

    if not conversation_file.exists() or not labels_file.exists():
        print(
            "Error: Multi-label ground truth files not found. Please run create_multilabel_ground_truth.py first."
        )
        return None, None

    with open(conversation_file, "r", encoding="utf-8") as f:
        conversations = json.load(f)

    with open(labels_file, "r", encoding="utf-8") as f:
        labels = json.load(f)

    # Limit for testing if specified
    if limit:
        conversations = conversations[:limit]

    # Create mappings
    label_map = {item["case_id"]: item for item in labels}

    return conversations, label_map


def map_model_output_to_multilabel(review_res):
    """
    Map model output to multi-label format.
    Returns dict with each category as True/False.
    """
    categories = {
        "consulting_company_info": False,
        "selling_user_info": False,
        "negative_news": False,
        "major_complaints": False,
        "request_contact_information": False,
        "spam_messages": False,
        "government_inquiry": False,
        "key_contact": False,
        "internal_system": False,
    }

    # Check sensitive_reply list
    sensitive_reply = review_res.get("sensitive_reply", [])
    for item in sensitive_reply:
        if item.get("hit_rule", False):
            if item.get("type") == "索要联系方式":
                categories["request_contact_information"] = True
            elif item.get("type") == "辱骂信息":
                categories["spam_messages"] = True

    # Check sensitive_inquiry list
    sensitive_inquiry = review_res.get("sensitive_inquiry", [])
    for item in sensitive_inquiry:
        if item.get("hit_rule", False):
            if item.get("type") == "重大客诉":
                categories["major_complaints"] = True
            elif item.get("type") == "负面新闻":
                categories["negative_news"] = True
            elif item.get("type") == "咨询公司信息":
                categories["consulting_company_info"] = True
            elif item.get("type") == "兜售用户信息":
                categories["selling_user_info"] = True

    # Check government_inquiry list
    government_inquiry = review_res.get("government_inquiry", [])
    for item in government_inquiry:
        if item.get("hit_rule", False):
            categories["government_inquiry"] = True

    # Check for key contact
    if review_res.get("key_contact", {}).get("hit_rule"):
        categories["key_contact"] = True

    # Check for internal system
    if review_res.get("internal_system", {}).get("hit_rule"):
        categories["internal_system"] = True

    return categories


def predict_with_model(conversations):
    """Use the model to predict categories for all conversations."""
    predictions = []

    try:
        from dc_ai_red_line_review.main_pipe import BasicPipeline

        pipeline = BasicPipeline()

        print(f"Processing {len(conversations)} conversations with your model...")

        for i, conv in enumerate(conversations):
            case_id = conv["case_id"]
            messages = conv["messages"]

            # Convert to your model's expected format
            model_messages = []
            for msg in messages:
                model_messages.append(
                    {
                        "id": msg["id"],
                        "type": msg["type"],
                        "msg": msg["content"],
                    }
                )

            try:
                if (i + 1) % 10 == 0:
                    print(
                        f"Processing case {case_id} ({i + 1}/{len(conversations)})..."
                    )

                # Run your model
                result = pipeline.run(messages=model_messages, caseId=str(case_id))

                # Extract review_res
                review_res = result.get("review_res", {})

                # Map to multi-label format
                predicted_labels = map_model_output_to_multilabel(review_res)

                predictions.append(
                    {"case_id": case_id, "predictions": predicted_labels}
                )

            except Exception as e:
                print(f"Error processing case {case_id}: {e}")
                # Default to all False
                default_labels = {
                    "consulting_company_info": False,
                    "selling_user_info": False,
                    "negative_news": False,
                    "major_complaints": False,
                    "request_contact_information": False,
                    "spam_messages": False,
                    "government_inquiry": False,
                    "key_contact": False,
                    "internal_system": False,
                }
                predictions.append({"case_id": case_id, "predictions": default_labels})

    except ImportError as e:
        print(f"Error importing model: {e}")
        return []

    return predictions


def evaluate_multilabel_predictions(predictions, label_map):
    """Evaluate multi-label predictions."""

    # Prepare data for evaluation
    category_names = [
        "consulting_company_info",
        "selling_user_info",
        "negative_news",
        "major_complaints",
        "request_contact_information",
        "spam_messages",
        "government_inquiry",
        "key_contact",
        "internal_system",
    ]

    y_true = []
    y_pred = []
    case_results = []

    for pred in predictions:
        case_id = pred["case_id"]
        if case_id in label_map:
            gt_labels = label_map[case_id]["labels"]
            pred_labels = pred["predictions"]

            # Convert to binary arrays
            gt_array = [gt_labels[cat] for cat in category_names]
            pred_array = [pred_labels[cat] for cat in category_names]

            y_true.append(gt_array)
            y_pred.append(pred_array)

            case_results.append(
                {
                    "case_id": case_id,
                    "ground_truth": gt_labels,
                    "predictions": pred_labels,
                    "exact_match": gt_labels == pred_labels,
                }
            )

    y_true = np.array(y_true)
    y_pred = np.array(y_pred)

    # Calculate metrics
    results = {}

    # Overall metrics
    results["exact_match_ratio"] = np.mean([r["exact_match"] for r in case_results])
    results["hamming_loss"] = hamming_loss(y_true, y_pred)
    results["jaccard_score"] = jaccard_score(y_true, y_pred, average="samples")

    # Per-category metrics
    category_metrics = {}
    for i, category in enumerate(category_names):
        y_true_cat = y_true[:, i]
        y_pred_cat = y_pred[:, i]

        if np.sum(y_true_cat) > 0:  # Only calculate if there are positive examples
            precision = precision_score(y_true_cat, y_pred_cat, zero_division=0)
            recall = recall_score(y_true_cat, y_pred_cat, zero_division=0)
            f1 = f1_score(y_true_cat, y_pred_cat, zero_division=0)
            support = np.sum(y_true_cat)

            category_metrics[category] = {
                "precision": precision,
                "recall": recall,
                "f1_score": f1,
                "support": support,
                "true_positives": np.sum((y_true_cat == 1) & (y_pred_cat == 1)),
                "false_positives": np.sum((y_true_cat == 0) & (y_pred_cat == 1)),
                "false_negatives": np.sum((y_true_cat == 1) & (y_pred_cat == 0)),
                "true_negatives": np.sum((y_true_cat == 0) & (y_pred_cat == 0)),
            }
        else:
            category_metrics[category] = {
                "precision": 0.0,
                "recall": 0.0,
                "f1_score": 0.0,
                "support": 0,
                "true_positives": 0,
                "false_positives": np.sum(y_pred_cat),
                "false_negatives": 0,
                "true_negatives": np.sum(1 - y_pred_cat),
            }

    results["category_metrics"] = category_metrics

    # Macro and micro averages
    results["macro_precision"] = np.mean(
        [m["precision"] for m in category_metrics.values()]
    )
    results["macro_recall"] = np.mean([m["recall"] for m in category_metrics.values()])
    results["macro_f1"] = np.mean([m["f1_score"] for m in category_metrics.values()])

    # Micro averages (weighted by support)
    total_tp = sum(m["true_positives"] for m in category_metrics.values())
    total_fp = sum(m["false_positives"] for m in category_metrics.values())
    total_fn = sum(m["false_negatives"] for m in category_metrics.values())

    results["micro_precision"] = (
        total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0
    )
    results["micro_recall"] = (
        total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0
    )
    results["micro_f1"] = (
        2
        * results["micro_precision"]
        * results["micro_recall"]
        / (results["micro_precision"] + results["micro_recall"])
        if (results["micro_precision"] + results["micro_recall"]) > 0
        else 0
    )

    # Error analysis
    results["case_results"] = case_results
    results["category_names"] = category_names

    return results


def print_multilabel_results(results):
    """Print comprehensive multi-label evaluation results."""
    print("\n" + "=" * 80)
    print("MULTI-LABEL EVALUATION RESULTS")
    print("=" * 80)

    # Overall metrics
    print(f"\n📊 OVERALL METRICS")
    print(f"Exact Match Ratio: {results['exact_match_ratio']:.3f}")
    print(f"Hamming Loss: {results['hamming_loss']:.3f}")
    print(f"Jaccard Score: {results['jaccard_score']:.3f}")

    print(f"\n📈 AGGREGATE METRICS")
    print(f"Macro Precision: {results['macro_precision']:.3f}")
    print(f"Macro Recall: {results['macro_recall']:.3f}")
    print(f"Macro F1-Score: {results['macro_f1']:.3f}")
    print(f"Micro Precision: {results['micro_precision']:.3f}")
    print(f"Micro Recall: {results['micro_recall']:.3f}")
    print(f"Micro F1-Score: {results['micro_f1']:.3f}")

    # Per-category results
    print(f"\n🎯 PER-CATEGORY METRICS")
    print(
        f"{'Category':<30} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<10}"
    )
    print("-" * 80)

    for category, metrics in results["category_metrics"].items():
        if metrics["support"] > 0:
            print(
                f"{category:<30} {metrics['precision']:<10.3f} {metrics['recall']:<10.3f} {metrics['f1_score']:<10.3f} {metrics['support']:<10}"
            )

    # Error analysis
    print(f"\n❌ ERROR ANALYSIS")
    exact_matches = sum(1 for r in results["case_results"] if r["exact_match"])
    total_cases = len(results["case_results"])
    print(
        f"Exact matches: {exact_matches}/{total_cases} ({exact_matches / total_cases * 100:.1f}%)"
    )

    # Show some mismatched cases
    mismatches = [r for r in results["case_results"] if not r["exact_match"]]
    print(f"\nFirst 5 mismatched cases:")
    for i, case in enumerate(mismatches[:5]):
        print(f"\nCase {case['case_id']}:")
        gt_active = [k for k, v in case["ground_truth"].items() if v]
        pred_active = [k for k, v in case["predictions"].items() if v]
        print(f"  GT:   {gt_active}")
        print(f"  Pred: {pred_active}")


def save_multilabel_results(
    results, output_file="data/multilabel_evaluation_results.json"
):
    """Save evaluation results to file."""

    # Convert numpy types to native Python types for JSON serialization
    def convert_numpy(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return obj

    # Deep convert all numpy types
    def deep_convert(obj):
        if isinstance(obj, dict):
            return {k: deep_convert(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [deep_convert(v) for v in obj]
        else:
            return convert_numpy(obj)

    serializable_results = deep_convert(results)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(serializable_results, f, indent=2, ensure_ascii=False)

    print(f"\nResults saved to {output_file}")


def main():
    """Main function for multi-label evaluation."""
    print("Loading multi-label ground truth data...")
    conversations, label_map = load_multilabel_ground_truth()

    if conversations is None:
        return

    print(f"Loaded {len(conversations)} conversations")

    print("\nRunning model predictions...")
    predictions = predict_with_model(conversations)

    if not predictions:
        print("No predictions generated")
        return

    print(f"\nGenerated {len(predictions)} predictions")
    print("\nEvaluating multi-label predictions...")
    results = evaluate_multilabel_predictions(predictions, label_map)

    # Print results
    print_multilabel_results(results)

    # Save results
    save_multilabel_results(results)

    print(f"\n✅ Multi-label evaluation complete!")
    print(f"📁 Results saved to data/multilabel_evaluation_results.json")


if __name__ == "__main__":
    main()
