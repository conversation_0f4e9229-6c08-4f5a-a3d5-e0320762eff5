#!/usr/bin/env python3
"""
Script to process ground truth data and evaluate model accuracy.
"""

import pandas as pd
import json
import re
from pathlib import Path
from collections import defaultdict
import sys
import os

# Add the current directory to Python path to import main_pipe
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clean_html_content(content):
    """Extract clean text from HTML/JSON content."""
    if not content:
        return ""
    
    # If it's JSON, try to parse it
    if content.strip().startswith('{'):
        try:
            data = json.loads(content)
            if 'content' in data:
                content = data['content']
        except:
            pass
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', ' ', content)
    # Decode HTML entities
    content = content.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    content = content.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    # Clean up whitespace
    content = re.sub(r'\s+', ' ', content).strip()
    
    return content

def load_and_process_gt_files():
    """Load all GT Excel files and extract annotated data."""
    gt_dir = Path("data/gt")
    excel_files = list(gt_dir.glob("gt_*.xlsx"))
    
    all_annotations = []
    
    for excel_file in excel_files:
        print(f"Processing {excel_file.name}...")
        df = pd.read_excel(excel_file)
        
        # Filter rows with human annotations
        annotated_rows = df[df['人工审核结果'].notna() | df['正确结果'].notna()]
        
        for _, row in annotated_rows.iterrows():
            annotation = {
                'file_source': excel_file.name,
                'case_id': row['f_case_id'],
                'msg_id': row['f_msg_id'],
                'user_type': row['f_user_type'],
                'original_msg': row['f_original_msg'],
                'keywords': row['f_keywords'],
                'f_type': row['f_type'],
                'f_sub_type': row['f_sub_type'],
                'human_review': row['人工审核结果'] if pd.notna(row['人工审核结果']) else None,
                'needs_optimization': row['是否需要优化模型'] if pd.notna(row['是否需要优化模型']) else None,
                'correct_result': row['正确结果'] if pd.notna(row['正确结果']) else None,
            }
            all_annotations.append(annotation)
    
    print(f"Total annotations found: {len(all_annotations)}")
    return all_annotations

def build_conversation_data(annotations):
    """Build conversation data from annotations."""
    # Group by case_id
    case_groups = defaultdict(list)
    for ann in annotations:
        case_groups[ann['case_id']].append(ann)
    
    conversations = []
    
    for case_id, msgs in case_groups.items():
        # Sort by msg_id to maintain order
        msgs.sort(key=lambda x: x['msg_id'])
        
        # Build messages list
        messages = []
        for i, msg in enumerate(msgs):
            # Clean the message content
            clean_content = clean_html_content(msg['original_msg'])
            if not clean_content:
                clean_content = msg['keywords']  # Fallback to keywords
            
            messages.append({
                'id': msg['msg_id'],
                'type': msg['user_type'],
                'msg': clean_content
            })
        
        # Get ground truth labels (use the first annotation with labels)
        ground_truth = None
        for msg in msgs:
            if msg['correct_result'] or msg['human_review']:
                ground_truth = {
                    'human_review': msg['human_review'],
                    'correct_result': msg['correct_result'],
                    'needs_optimization': msg['needs_optimization'],
                    'original_type': msg['f_type'],
                    'original_sub_type': msg['f_sub_type']
                }
                break
        
        conversations.append({
            'case_id': case_id,
            'messages': messages,
            'ground_truth': ground_truth,
            'source_files': list(set(msg['file_source'] for msg in msgs))
        })
    
    print(f"Built {len(conversations)} conversations")
    return conversations

def run_model_prediction(conversations):
    """Run model predictions on conversations."""
    try:
        from dc_ai_red_line_review_langextract.main_pipe import BasicPipeline
        
        pipeline = BasicPipeline()
        results = []
        
        for conv in conversations:
            try:
                print(f"Processing case {conv['case_id']}...")
                result = pipeline.run(messages=conv['messages'], caseId=str(conv['case_id']))
                results.append({
                    'case_id': conv['case_id'],
                    'prediction': result,
                    'ground_truth': conv['ground_truth'],
                    'source_files': conv['source_files']
                })
            except Exception as e:
                print(f"Error processing case {conv['case_id']}: {e}")
                results.append({
                    'case_id': conv['case_id'],
                    'prediction': None,
                    'error': str(e),
                    'ground_truth': conv['ground_truth'],
                    'source_files': conv['source_files']
                })
        
        return results
        
    except ImportError as e:
        print(f"Error importing main_pipe: {e}")
        return None

def analyze_results(results):
    """Analyze prediction results against ground truth."""
    if not results:
        print("No results to analyze")
        return
    
    analysis = {
        'total_cases': len(results),
        'successful_predictions': 0,
        'failed_predictions': 0,
        'accuracy_metrics': {},
        'detailed_results': []
    }
    
    for result in results:
        if result.get('error'):
            analysis['failed_predictions'] += 1
            continue
            
        analysis['successful_predictions'] += 1
        
        case_analysis = {
            'case_id': result['case_id'],
            'ground_truth': result['ground_truth'],
            'prediction': result['prediction'],
            'source_files': result['source_files']
        }
        
        # Extract prediction categories
        if result['prediction'] and 'review_res' in result['prediction']:
            review_res = result['prediction']['review_res']
            
            # Check for different types of alerts
            alerts = []
            for category, data in review_res.items():
                if isinstance(data, dict) and data.get('hit_rule'):
                    alerts.append(category)
                elif isinstance(data, list) and data:
                    alerts.append(category)
            
            case_analysis['predicted_alerts'] = alerts
            case_analysis['has_alerts'] = len(alerts) > 0
        else:
            case_analysis['predicted_alerts'] = []
            case_analysis['has_alerts'] = False
        
        # Compare with ground truth
        gt = result['ground_truth']
        if gt:
            # Determine if ground truth indicates an issue
            gt_has_issue = False
            if gt['correct_result'] and '无异常' not in gt['correct_result']:
                gt_has_issue = True
            elif gt['human_review'] and '无异常' not in gt['human_review']:
                gt_has_issue = True
            
            case_analysis['gt_has_issue'] = gt_has_issue
            case_analysis['prediction_correct'] = (case_analysis['has_alerts'] == gt_has_issue)
        
        analysis['detailed_results'].append(case_analysis)
    
    # Calculate overall accuracy
    correct_predictions = sum(1 for r in analysis['detailed_results'] 
                            if r.get('prediction_correct', False))
    total_with_gt = sum(1 for r in analysis['detailed_results'] 
                       if r['ground_truth'] is not None)
    
    if total_with_gt > 0:
        analysis['accuracy_metrics']['overall_accuracy'] = correct_predictions / total_with_gt
        analysis['accuracy_metrics']['correct_predictions'] = correct_predictions
        analysis['accuracy_metrics']['total_with_ground_truth'] = total_with_gt
    
    return analysis

def save_results(analysis, output_dir="data/evaluation_results"):
    """Save analysis results to files."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save detailed results
    with open(output_path / "detailed_results.json", 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    
    # Save summary report
    with open(output_path / "summary_report.md", 'w', encoding='utf-8') as f:
        f.write("# Model Evaluation Report\n\n")
        f.write(f"## Summary\n")
        f.write(f"- Total cases: {analysis['total_cases']}\n")
        f.write(f"- Successful predictions: {analysis['successful_predictions']}\n")
        f.write(f"- Failed predictions: {analysis['failed_predictions']}\n")
        
        if 'accuracy_metrics' in analysis and analysis['accuracy_metrics']:
            metrics = analysis['accuracy_metrics']
            f.write(f"\n## Accuracy Metrics\n")
            f.write(f"- Overall accuracy: {metrics.get('overall_accuracy', 0):.2%}\n")
            f.write(f"- Correct predictions: {metrics.get('correct_predictions', 0)}\n")
            f.write(f"- Total with ground truth: {metrics.get('total_with_ground_truth', 0)}\n")
        
        f.write(f"\n## Detailed Results\n")
        for result in analysis['detailed_results'][:10]:  # Show first 10
            f.write(f"\n### Case {result['case_id']}\n")
            f.write(f"- Predicted alerts: {result.get('predicted_alerts', [])}\n")
            f.write(f"- Has alerts: {result.get('has_alerts', False)}\n")
            f.write(f"- Ground truth has issue: {result.get('gt_has_issue', 'Unknown')}\n")
            f.write(f"- Prediction correct: {result.get('prediction_correct', 'Unknown')}\n")
    
    print(f"Results saved to {output_path}")

def main():
    """Main function."""
    print("Starting ground truth processing and evaluation...")
    
    # Step 1: Load and process GT files
    annotations = load_and_process_gt_files()
    
    # Step 2: Build conversation data
    conversations = build_conversation_data(annotations)
    
    # Step 3: Run model predictions
    print("\nRunning model predictions...")
    results = run_model_prediction(conversations)
    
    if results is None:
        print("Failed to run predictions")
        return
    
    # Step 4: Analyze results
    print("\nAnalyzing results...")
    analysis = analyze_results(results)
    
    # Step 5: Save results
    save_results(analysis)
    
    # Print summary
    print(f"\n=== EVALUATION SUMMARY ===")
    print(f"Total cases processed: {analysis['total_cases']}")
    print(f"Successful predictions: {analysis['successful_predictions']}")
    print(f"Failed predictions: {analysis['failed_predictions']}")
    
    if 'accuracy_metrics' in analysis and analysis['accuracy_metrics']:
        metrics = analysis['accuracy_metrics']
        print(f"Overall accuracy: {metrics.get('overall_accuracy', 0):.2%}")
        print(f"Correct predictions: {metrics.get('correct_predictions', 0)}")
        print(f"Total with ground truth: {metrics.get('total_with_ground_truth', 0)}")

if __name__ == "__main__":
    main()
