#!/usr/bin/env python3
"""
Quick test version - evaluate only first 10 conversations for testing.
"""

import json
import sys
import os
from pathlib import Path
from collections import defaultdict
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    confusion_matrix,
    classification_report,
)

# Add your model path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def load_ground_truth_data(limit=10):
    """Load the ground truth dataset by category."""
    conversation_file = Path("data/ground_truth_by_category/conversation_data.json")
    labels_file = Path("data/ground_truth_by_category/labels_by_category.json")

    if not conversation_file.exists() or not labels_file.exists():
        print(
            "Error: Ground truth files not found. Please run create_correct_ground_truth.py first."
        )
        return None, None

    with open(conversation_file, "r", encoding="utf-8") as f:
        conversations = json.load(f)

    with open(labels_file, "r", encoding="utf-8") as f:
        labels = json.load(f)

    # Limit for quick testing
    conversations = conversations[:limit]

    # Create mappings
    label_map = {item["case_id"]: item for item in labels}

    return conversations, label_map


def map_model_output_to_category(review_res):
    """Map model output to ground truth categories."""
    # Check sensitive_reply list first (highest priority)
    sensitive_reply = review_res.get("sensitive_reply", [])
    for item in sensitive_reply:
        if item.get("hit_rule", False):
            if item.get("type") == "索要联系方式":
                return "敏感回复-索要联系方式"
            elif item.get("type") == "辱骂信息":
                return "敏感回复-辱骂信息"

    # Check sensitive_inquiry list
    sensitive_inquiry = review_res.get("sensitive_inquiry", [])
    for item in sensitive_inquiry:
        if item.get("hit_rule", False):
            if item.get("type") == "重大客诉":
                return "敏感咨询-重大客诉"
            elif item.get("type") == "负面新闻":
                return "敏感咨询-负面新闻"
            elif item.get("type") == "咨询公司信息":
                return "敏感咨询-咨询公司信息"
            elif item.get("type") == "兜售用户信息":
                return "敏感回复-索要联系方式"  # Map to contact info category

    # Check government_inquiry list
    government_inquiry = review_res.get("government_inquiry", [])
    for item in government_inquiry:
        if item.get("hit_rule", False):
            if item.get("type") == "政府机构":
                return "政府咨询-政府机构"
            elif item.get("type") == "政府邮箱":
                return "政府咨询-政府邮箱"

    # Check for key contact
    if review_res.get("key_contact", {}).get("hit_rule"):
        return "关键联系人-(null)"

    # Check for internal system (might be mapped to key contact)
    if review_res.get("internal_system", {}).get("hit_rule"):
        return "关键联系人-(null)"

    # Default: no issues detected
    return "无异常"


def predict_with_your_model(conversations):
    """Use your actual model to predict categories."""
    predictions = []

    try:
        from dc_ai_red_line_review.main_pipe import BasicPipeline

        pipeline = BasicPipeline()

        print(f"Processing {len(conversations)} conversations with your model...")

        for i, conv in enumerate(conversations):
            case_id = conv["case_id"]
            messages = conv["messages"]

            # Convert to your model's expected format
            model_messages = []
            for msg in messages:
                model_messages.append(
                    {
                        "id": msg["id"],
                        "type": msg["type"],
                        "msg": msg["content"],
                    }
                )

            try:
                print(f"Processing case {case_id} ({i + 1}/{len(conversations)})...")

                # Run your model
                result = pipeline.run(messages=model_messages, caseId=str(case_id))

                # Extract review_res
                review_res = result.get("review_res", {})

                # Map to category
                predicted_category = map_model_output_to_category(review_res)

                predictions.append(
                    {"case_id": case_id, "prediction": predicted_category}
                )

                print(f"  -> Predicted: {predicted_category}")

            except Exception as e:
                print(f"Error processing case {case_id}: {e}")
                predictions.append(
                    {
                        "case_id": case_id,
                        "prediction": "无异常",  # Default to no issue
                    }
                )

    except ImportError as e:
        print(f"Error importing model: {e}")
        return []

    return predictions


def evaluate_quick_test(predictions, label_map):
    """Quick evaluation for testing."""

    # Prepare data for evaluation
    results = []

    for pred in predictions:
        case_id = pred["case_id"]
        if case_id in label_map:
            gt_label = label_map[case_id]["ground_truth"]
            pred_label = pred["prediction"]
            is_correct = gt_label == pred_label

            results.append(
                {
                    "case_id": case_id,
                    "ground_truth": gt_label,
                    "prediction": pred_label,
                    "correct": is_correct,
                    "model_original": label_map[case_id]["model_prediction"],
                }
            )

    # Calculate simple accuracy
    correct_count = sum(1 for r in results if r["correct"])
    total_count = len(results)
    accuracy = correct_count / total_count if total_count > 0 else 0

    return {
        "accuracy": accuracy,
        "correct": correct_count,
        "total": total_count,
        "results": results,
    }


def print_quick_results(evaluation):
    """Print quick test results."""
    print("\n" + "=" * 60)
    print("QUICK TEST RESULTS")
    print("=" * 60)

    print(
        f"Accuracy: {evaluation['accuracy']:.2%} ({evaluation['correct']}/{evaluation['total']})"
    )

    print(f"\nDetailed Results:")
    for result in evaluation["results"]:
        status = "✓" if result["correct"] else "✗"
        print(
            f"{status} Case {result['case_id']}: {result['ground_truth']} -> {result['prediction']}"
        )
        if not result["correct"]:
            print(f"    (Original model prediction: {result['model_original']})")


def main():
    """Main function for quick testing."""
    print("Quick Test: Loading first 10 conversations...")
    conversations, label_map = load_ground_truth_data(limit=10)

    if conversations is None:
        return

    print(f"Loaded {len(conversations)} conversations for testing")

    print("\nRunning model predictions...")
    predictions = predict_with_your_model(conversations)

    if not predictions:
        print("No predictions generated")
        return

    print("\nEvaluating predictions...")
    evaluation = evaluate_quick_test(predictions, label_map)

    # Print results
    print_quick_results(evaluation)

    # Save quick results
    output_file = "data/quick_test_results.json"
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(evaluation, f, indent=2, ensure_ascii=False)

    print(f"\nQuick test results saved to {output_file}")


if __name__ == "__main__":
    main()
