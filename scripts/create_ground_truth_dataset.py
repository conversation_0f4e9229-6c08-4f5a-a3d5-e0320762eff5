#!/usr/bin/env python3
"""
Create a clean ground truth dataset based on human annotations.
This script generates:
1. Clean conversation data
2. Binary labels (has_issue/no_issue) for each conversation
3. Evaluation-ready format
"""

import json
import re
from pathlib import Path
from collections import defaultdict

def clean_html_content(content):
    """Extract clean text from HTML/JSON content."""
    if not content:
        return ""
    
    # If it's JSON, try to parse it
    if content.strip().startswith('{'):
        try:
            data = json.loads(content)
            if 'content' in data:
                content = data['content']
        except:
            pass
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', ' ', content)
    # Decode HTML entities
    content = content.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    content = content.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    # Clean up whitespace
    content = re.sub(r'\s+', ' ', content).strip()
    
    return content

def determine_ground_truth_label(ground_truth):
    """
    Determine if a conversation has issues based on human annotations.
    Returns: True if has issues, False if no issues, None if unclear
    """
    if not ground_truth:
        return None
    
    human_review = ground_truth.get('human_review', '')
    correct_result = ground_truth.get('correct_result', '')
    
    # Keywords that indicate NO issues
    no_issue_keywords = [
        '无异常',
        '业务模板',
        '正常业务',
        '公共邮箱',
        '官方邮箱',
        '用户自己的信息',
        '用户自身信息'
    ]
    
    # Keywords that indicate ISSUES
    issue_keywords = [
        '潜在风险',
        '确实存在风险',
        '风险',
        '异常',
        '问题'
    ]
    
    # Keywords that indicate classification errors (treat as no issue for the content itself)
    classification_error_keywords = [
        '大分类不对',
        '分类不对',
        '实际为用户咨询',
        '实际为客服回复',
        '不是政府机构咨询',
        '而非辱骂信息',
        '内容实际是邮件系统'
    ]
    
    # Check correct_result first (more authoritative)
    if correct_result:
        if '无异常' in correct_result or '不告警' in correct_result:
            return False
        elif any(keyword in correct_result for keyword in issue_keywords):
            return True
    
    # Check human_review
    if human_review:
        # If it's a classification error, treat as no content issue
        if any(keyword in human_review for keyword in classification_error_keywords):
            return False
        
        # Check for explicit no issue indicators
        if any(keyword in human_review for keyword in no_issue_keywords):
            return False
        
        # Check for issue indicators
        if any(keyword in human_review for keyword in issue_keywords):
            return True
    
    # Default: if we can't determine, return None
    return None

def create_ground_truth_dataset():
    """Create the ground truth dataset."""
    
    # Load processed conversations
    conversations_file = Path("data/processed_gt/conversations.json")
    if not conversations_file.exists():
        print("Error: conversations.json not found. Please run process_gt_data_only.py first.")
        return
    
    with open(conversations_file, 'r', encoding='utf-8') as f:
        conversations = json.load(f)
    
    # Process each conversation
    gt_dataset = []
    stats = {
        'total_conversations': len(conversations),
        'has_ground_truth': 0,
        'no_ground_truth': 0,
        'has_issues': 0,
        'no_issues': 0,
        'unclear_labels': 0,
        'by_original_type': defaultdict(int),
        'by_original_subtype': defaultdict(int),
        'label_distribution': defaultdict(int)
    }
    
    for conv in conversations:
        case_id = conv['case_id']
        messages = conv['messages']
        ground_truth = conv['ground_truth']
        
        # Clean messages
        clean_messages = []
        for msg in messages:
            clean_content = clean_html_content(msg['msg'])
            if clean_content:  # Only include non-empty messages
                clean_messages.append({
                    'id': msg['id'],
                    'type': msg['type'],
                    'content': clean_content
                })
        
        # Skip conversations with no clean messages
        if not clean_messages:
            continue
        
        # Determine ground truth label
        if ground_truth:
            stats['has_ground_truth'] += 1
            has_issue = determine_ground_truth_label(ground_truth)
            
            # Track original categories
            if ground_truth.get('original_type'):
                stats['by_original_type'][ground_truth['original_type']] += 1
            if ground_truth.get('original_sub_type'):
                stats['by_original_subtype'][ground_truth['original_sub_type']] += 1
            
            if has_issue is True:
                label = 'has_issue'
                stats['has_issues'] += 1
            elif has_issue is False:
                label = 'no_issue'
                stats['no_issues'] += 1
            else:
                label = 'unclear'
                stats['unclear_labels'] += 1
            
            stats['label_distribution'][label] += 1
            
        else:
            stats['no_ground_truth'] += 1
            label = None
        
        # Create dataset entry
        dataset_entry = {
            'case_id': case_id,
            'messages': clean_messages,
            'label': label,
            'human_annotations': {
                'human_review': ground_truth.get('human_review') if ground_truth else None,
                'correct_result': ground_truth.get('correct_result') if ground_truth else None,
                'needs_optimization': ground_truth.get('needs_optimization') if ground_truth else None,
                'original_type': ground_truth.get('original_type') if ground_truth else None,
                'original_sub_type': ground_truth.get('original_sub_type') if ground_truth else None
            },
            'source_files': conv['source_files'],
            'message_count': len(clean_messages)
        }
        
        gt_dataset.append(dataset_entry)
    
    return gt_dataset, stats

def save_ground_truth_dataset(gt_dataset, stats, output_dir="data/ground_truth"):
    """Save the ground truth dataset."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Filter dataset by label availability
    labeled_dataset = [entry for entry in gt_dataset if entry['label'] is not None]
    evaluation_dataset = [entry for entry in labeled_dataset if entry['label'] != 'unclear']
    
    # Save full dataset
    with open(output_path / "full_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(gt_dataset, f, indent=2, ensure_ascii=False)
    
    # Save labeled dataset only
    with open(output_path / "labeled_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(labeled_dataset, f, indent=2, ensure_ascii=False)
    
    # Save evaluation-ready dataset (clear labels only)
    with open(output_path / "evaluation_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(evaluation_dataset, f, indent=2, ensure_ascii=False)
    
    # Save conversation data only (for model input)
    conversation_data = []
    for entry in evaluation_dataset:
        conversation_data.append({
            'case_id': entry['case_id'],
            'messages': entry['messages']
        })
    
    with open(output_path / "conversation_data.json", 'w', encoding='utf-8') as f:
        json.dump(conversation_data, f, indent=2, ensure_ascii=False)
    
    # Save labels only (for evaluation)
    labels_data = []
    for entry in evaluation_dataset:
        labels_data.append({
            'case_id': entry['case_id'],
            'label': entry['label'],
            'has_issue': entry['label'] == 'has_issue'
        })
    
    with open(output_path / "labels.json", 'w', encoding='utf-8') as f:
        json.dump(labels_data, f, indent=2, ensure_ascii=False)
    
    # Save statistics and summary
    with open(output_path / "dataset_stats.json", 'w', encoding='utf-8') as f:
        json.dump(dict(stats), f, indent=2, ensure_ascii=False)
    
    # Create summary report
    with open(output_path / "dataset_summary.md", 'w', encoding='utf-8') as f:
        f.write("# Ground Truth Dataset Summary\n\n")
        
        f.write("## Dataset Statistics\n")
        f.write(f"- Total conversations: {stats['total_conversations']}\n")
        f.write(f"- With ground truth: {stats['has_ground_truth']}\n")
        f.write(f"- Without ground truth: {stats['no_ground_truth']}\n")
        f.write(f"- Evaluation-ready: {len(evaluation_dataset)}\n\n")
        
        f.write("## Label Distribution\n")
        for label, count in stats['label_distribution'].items():
            percentage = count / stats['has_ground_truth'] * 100 if stats['has_ground_truth'] > 0 else 0
            f.write(f"- {label}: {count} ({percentage:.1f}%)\n")
        f.write("\n")
        
        f.write("## Original Type Distribution\n")
        for type_name, count in stats['by_original_type'].items():
            f.write(f"- {type_name}: {count}\n")
        f.write("\n")
        
        f.write("## Original Sub-Type Distribution\n")
        for subtype, count in stats['by_original_subtype'].items():
            f.write(f"- {subtype}: {count}\n")
        f.write("\n")
        
        f.write("## Files Generated\n")
        f.write("- `full_dataset.json`: Complete dataset with all conversations\n")
        f.write("- `labeled_dataset.json`: Only conversations with human labels\n")
        f.write("- `evaluation_dataset.json`: Only conversations with clear labels (has_issue/no_issue)\n")
        f.write("- `conversation_data.json`: Clean conversation data for model input\n")
        f.write("- `labels.json`: Ground truth labels for evaluation\n")
        f.write("- `dataset_stats.json`: Detailed statistics\n\n")
        
        f.write("## Usage for Model Evaluation\n")
        f.write("1. Use `conversation_data.json` as input to your model\n")
        f.write("2. Compare model predictions with `labels.json`\n")
        f.write("3. Calculate accuracy, precision, recall, F1-score\n\n")
        
        f.write("## Sample Entries\n")
        for i, entry in enumerate(evaluation_dataset[:3]):
            f.write(f"\n### Case {entry['case_id']} (Label: {entry['label']})\n")
            f.write(f"- Messages: {entry['message_count']}\n")
            f.write(f"- Human review: {entry['human_annotations']['human_review']}\n")
            f.write(f"- Correct result: {entry['human_annotations']['correct_result']}\n")
            f.write(f"- Sample message: {entry['messages'][0]['content'][:150]}...\n")
    
    print(f"Ground truth dataset saved to {output_path}")
    return output_path, len(evaluation_dataset)

def main():
    """Main function."""
    print("Creating ground truth dataset...")
    
    # Create dataset
    gt_dataset, stats = create_ground_truth_dataset()
    
    if not gt_dataset:
        print("Failed to create dataset")
        return
    
    # Save dataset
    output_path, eval_count = save_ground_truth_dataset(gt_dataset, stats)
    
    # Print summary
    print(f"\n=== GROUND TRUTH DATASET CREATED ===")
    print(f"Total conversations: {stats['total_conversations']}")
    print(f"With ground truth: {stats['has_ground_truth']}")
    print(f"Evaluation-ready: {eval_count}")
    print(f"Label distribution:")
    for label, count in stats['label_distribution'].items():
        percentage = count / stats['has_ground_truth'] * 100 if stats['has_ground_truth'] > 0 else 0
        print(f"  {label}: {count} ({percentage:.1f}%)")
    
    print(f"\nFiles saved to: {output_path}")
    print(f"\nFor model evaluation:")
    print(f"  - Input: conversation_data.json ({eval_count} conversations)")
    print(f"  - Labels: labels.json ({eval_count} labels)")

if __name__ == "__main__":
    main()
