#!/usr/bin/env python3
"""
Create correct multi-label ground truth dataset.
This script generates GT labels for each category based on:
1. Model's original predictions (f_type + f_sub_type)
2. Human review corrections
3. Multi-label format (each category gets True/False)
"""

import json
import re
from pathlib import Path
from collections import defaultdict

def clean_html_content(content):
    """Extract clean text from HTML/JSON content."""
    if not content:
        return ""
    
    # If it's JSON, try to parse it
    if content.strip().startswith('{'):
        try:
            data = json.loads(content)
            if 'content' in data:
                content = data['content']
        except:
            pass
    
    # Remove HTML tags
    content = re.sub(r'<[^>]+>', ' ', content)
    # Decode HTML entities
    content = content.replace('&lt;', '<').replace('&gt;', '>').replace('&amp;', '&')
    content = content.replace('&quot;', '"').replace('&#39;', "'").replace('&nbsp;', ' ')
    # Clean up whitespace
    content = re.sub(r'\s+', ' ', content).strip()
    
    return content

def map_original_prediction_to_categories(f_type, f_sub_type):
    """
    Map original model prediction to category labels.
    Returns dict with each category as True/False.
    """
    categories = {
        'consulting_company_info': False,
        'selling_user_info': False, 
        'negative_news': False,
        'major_complaints': False,
        'request_contact_information': False,
        'spam_messages': False,
        'government_inquiry': False,
        'key_contact': False,
        'internal_system': False
    }
    
    # Map based on f_type and f_sub_type
    if f_type == "敏感咨询":
        if f_sub_type == "重大客诉":
            categories['major_complaints'] = True
        elif f_sub_type == "负面新闻":
            categories['negative_news'] = True
        elif f_sub_type == "咨询公司信息":
            categories['consulting_company_info'] = True
        elif f_sub_type == "兜售用户信息":
            categories['selling_user_info'] = True
    
    elif f_type == "敏感回复":
        if f_sub_type == "索要联系方式":
            categories['request_contact_information'] = True
        elif f_sub_type == "辱骂信息":
            categories['spam_messages'] = True
    
    elif f_type == "政府咨询":
        categories['government_inquiry'] = True
    
    elif f_type == "关键联系人":
        categories['key_contact'] = True
    
    return categories

def determine_ground_truth_labels(annotation):
    """
    Determine ground truth labels based on model prediction + human review.
    
    Logic:
    1. Start with model's original prediction
    2. If human says "无异常" -> GT = model prediction (model is correct)
    3. If human gives "正确结果" -> GT = parse correct result
    4. If human says "分类不对" -> GT = no categories (all False)
    """
    f_type = annotation.get('f_type', '')
    f_sub_type = annotation.get('f_sub_type', '')
    human_review = annotation.get('human_review', '')
    correct_result = annotation.get('correct_result', '')
    
    # Start with model's original prediction
    gt_labels = map_original_prediction_to_categories(f_type, f_sub_type)
    
    # Apply human corrections
    if correct_result:
        if '无异常' in correct_result or '不告警' in correct_result:
            # Human says model is correct, keep original prediction
            pass
        else:
            # Human gives specific correct result, parse it
            gt_labels = parse_correct_result(correct_result)
    
    elif human_review:
        # Keywords indicating model is correct
        correct_keywords = [
            '无异常',
            '业务模板',
            '正常业务',
            '公共邮箱',
            '官方邮箱'
        ]
        
        # Keywords indicating classification errors
        error_keywords = [
            '大分类不对',
            '分类不对',
            '实际为用户咨询',
            '实际为客服回复',
            '不是政府机构咨询',
            '而非辱骂信息',
            '内容实际是邮件系统'
        ]
        
        if any(keyword in human_review for keyword in correct_keywords):
            # Human says model is correct, keep original prediction
            pass
        elif any(keyword in human_review for keyword in error_keywords):
            # Human says classification is wrong, set all to False
            gt_labels = {k: False for k in gt_labels.keys()}
        elif '潜在风险' in human_review:
            # Keep original prediction but note it's risky
            pass
    
    return gt_labels

def parse_correct_result(correct_result):
    """Parse human-provided correct result into category labels."""
    categories = {
        'consulting_company_info': False,
        'selling_user_info': False,
        'negative_news': False,
        'major_complaints': False,
        'request_contact_information': False,
        'spam_messages': False,
        'government_inquiry': False,
        'key_contact': False,
        'internal_system': False
    }
    
    # Parse the correct result string
    if '敏感咨询-重大客诉' in correct_result:
        categories['major_complaints'] = True
    elif '敏感咨询-负面新闻' in correct_result:
        categories['negative_news'] = True
    elif '敏感咨询-咨询公司信息' in correct_result:
        categories['consulting_company_info'] = True
    elif '敏感咨询-兜售用户信息' in correct_result:
        categories['selling_user_info'] = True
    elif '敏感回复-索要联系方式' in correct_result:
        categories['request_contact_information'] = True
    elif '敏感回复-辱骂信息' in correct_result:
        categories['spam_messages'] = True
    elif '政府咨询' in correct_result:
        categories['government_inquiry'] = True
    elif '关键联系人' in correct_result:
        categories['key_contact'] = True
    
    return categories

def create_multilabel_ground_truth():
    """Create multi-label ground truth dataset."""
    
    # Load processed annotations
    annotations_file = Path("data/processed_gt/annotations.json")
    if not annotations_file.exists():
        print("Error: annotations.json not found. Please run process_gt_data_only.py first.")
        return None
    
    with open(annotations_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # Group by case_id
    case_groups = defaultdict(list)
    for ann in annotations:
        case_groups[ann['case_id']].append(ann)
    
    # Process each case
    gt_dataset = []
    stats = {
        'total_cases': len(case_groups),
        'category_counts': defaultdict(int),
        'human_corrections': 0,
        'model_correct': 0,
        'multi_label_cases': 0
    }
    
    for case_id, case_annotations in case_groups.items():
        # Sort by msg_id to maintain order
        case_annotations.sort(key=lambda x: x['msg_id'])
        
        # Build clean messages
        clean_messages = []
        for ann in case_annotations:
            clean_content = clean_html_content(ann['original_msg'])
            if clean_content:
                clean_messages.append({
                    'id': ann['msg_id'],
                    'type': ann['user_type'],
                    'content': clean_content
                })
        
        if not clean_messages:
            continue
        
        # Get the primary annotation (first one with labels)
        primary_annotation = None
        for ann in case_annotations:
            if ann.get('human_review') or ann.get('correct_result'):
                primary_annotation = ann
                break
        
        if not primary_annotation:
            continue
        
        # Determine ground truth labels for each category
        gt_labels = determine_ground_truth_labels(primary_annotation)
        
        # Count active categories
        active_categories = sum(1 for v in gt_labels.values() if v)
        if active_categories > 1:
            stats['multi_label_cases'] += 1
        
        # Update statistics
        for category, is_active in gt_labels.items():
            if is_active:
                stats['category_counts'][category] += 1
        
        # Check if human made corrections
        original_labels = map_original_prediction_to_categories(
            primary_annotation.get('f_type', ''),
            primary_annotation.get('f_sub_type', '')
        )
        
        if gt_labels != original_labels:
            stats['human_corrections'] += 1
        else:
            stats['model_correct'] += 1
        
        # Create dataset entry
        dataset_entry = {
            'case_id': case_id,
            'messages': clean_messages,
            'ground_truth_labels': gt_labels,
            'original_prediction': {
                'f_type': primary_annotation.get('f_type'),
                'f_sub_type': primary_annotation.get('f_sub_type'),
                'labels': original_labels
            },
            'human_annotations': {
                'human_review': primary_annotation.get('human_review'),
                'correct_result': primary_annotation.get('correct_result'),
                'needs_optimization': primary_annotation.get('needs_optimization')
            },
            'source_files': list(set(ann['file_source'] for ann in case_annotations)),
            'message_count': len(clean_messages)
        }
        
        gt_dataset.append(dataset_entry)
    
    return gt_dataset, stats

def save_multilabel_dataset(gt_dataset, stats, output_dir="data/multilabel_ground_truth"):
    """Save the multi-label ground truth dataset."""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Save full dataset
    with open(output_path / "full_dataset.json", 'w', encoding='utf-8') as f:
        json.dump(gt_dataset, f, indent=2, ensure_ascii=False)
    
    # Create conversation data for model input
    conversation_data = []
    for entry in gt_dataset:
        conversation_data.append({
            'case_id': entry['case_id'],
            'messages': entry['messages']
        })
    
    with open(output_path / "conversation_data.json", 'w', encoding='utf-8') as f:
        json.dump(conversation_data, f, indent=2, ensure_ascii=False)
    
    # Create labels in multi-label format
    labels_data = []
    for entry in gt_dataset:
        labels_data.append({
            'case_id': entry['case_id'],
            'labels': entry['ground_truth_labels'],
            'original_prediction': entry['original_prediction']['labels']
        })
    
    with open(output_path / "multilabel_labels.json", 'w', encoding='utf-8') as f:
        json.dump(labels_data, f, indent=2, ensure_ascii=False)
    
    # Save statistics
    stats_serializable = {}
    for key, value in stats.items():
        if isinstance(value, defaultdict):
            stats_serializable[key] = dict(value)
        else:
            stats_serializable[key] = value
    
    with open(output_path / "dataset_stats.json", 'w', encoding='utf-8') as f:
        json.dump(stats_serializable, f, indent=2, ensure_ascii=False)
    
    # Create summary report
    with open(output_path / "dataset_summary.md", 'w', encoding='utf-8') as f:
        f.write("# Multi-Label Ground Truth Dataset\n\n")
        
        f.write("## Dataset Statistics\n")
        f.write(f"- Total cases: {stats['total_cases']}\n")
        f.write(f"- Model correct: {stats['model_correct']} ({stats['model_correct']/stats['total_cases']*100:.1f}%)\n")
        f.write(f"- Human corrections: {stats['human_corrections']} ({stats['human_corrections']/stats['total_cases']*100:.1f}%)\n")
        f.write(f"- Multi-label cases: {stats['multi_label_cases']}\n\n")
        
        f.write("## Category Distribution (Ground Truth)\n")
        for category, count in sorted(stats['category_counts'].items(), key=lambda x: x[1], reverse=True):
            f.write(f"- {category}: {count}\n")
        f.write("\n")
        
        f.write("## Files Generated\n")
        f.write("- `full_dataset.json`: Complete dataset with all information\n")
        f.write("- `conversation_data.json`: Clean conversation data for model input\n")
        f.write("- `multilabel_labels.json`: Multi-label ground truth labels\n")
        f.write("- `dataset_stats.json`: Detailed statistics\n\n")
        
        f.write("## Usage for Multi-Label Evaluation\n")
        f.write("1. Use `conversation_data.json` as input to your model\n")
        f.write("2. Compare model predictions with `multilabel_labels.json`\n")
        f.write("3. Calculate per-category precision, recall, F1-score\n")
        f.write("4. Calculate overall metrics (micro/macro averages)\n\n")
        
        f.write("## Sample Entries\n")
        for i, entry in enumerate(gt_dataset[:3]):
            f.write(f"\n### Case {entry['case_id']}\n")
            f.write(f"- Original prediction: {entry['original_prediction']['f_type']}-{entry['original_prediction']['f_sub_type']}\n")
            active_gt = [k for k, v in entry['ground_truth_labels'].items() if v]
            f.write(f"- Ground truth categories: {active_gt}\n")
            f.write(f"- Human review: {entry['human_annotations']['human_review']}\n")
            f.write(f"- Messages: {entry['message_count']}\n")
    
    print(f"Multi-label ground truth dataset saved to {output_path}")
    return output_path

def main():
    """Main function."""
    print("Creating multi-label ground truth dataset...")
    
    # Create dataset
    gt_dataset, stats = create_multilabel_ground_truth()
    
    if gt_dataset is None:
        print("Failed to create dataset")
        return
    
    # Save dataset
    output_path = save_multilabel_dataset(gt_dataset, stats)
    
    # Print summary
    print(f"\n=== MULTI-LABEL GROUND TRUTH DATASET ===")
    print(f"Total cases: {stats['total_cases']}")
    print(f"Model correct: {stats['model_correct']} ({stats['model_correct']/stats['total_cases']*100:.1f}%)")
    print(f"Human corrections: {stats['human_corrections']} ({stats['human_corrections']/stats['total_cases']*100:.1f}%)")
    print(f"Multi-label cases: {stats['multi_label_cases']}")
    
    print(f"\nCategory distribution:")
    for category, count in sorted(stats['category_counts'].items(), key=lambda x: x[1], reverse=True):
        print(f"  {category}: {count}")
    
    print(f"\nFiles saved to: {output_path}")

if __name__ == "__main__":
    main()
