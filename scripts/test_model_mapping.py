#!/usr/bin/env python3
"""
Test script to verify the model output mapping logic.
"""

import json
from dc_ai_red_line_review_langextract.main_pipe import BasicPipeline

def test_model_output():
    """Test the model with a sample conversation to see the output format."""
    
    pipeline = BasicPipeline()
    
    # Test with a simple conversation
    test_messages = [
        {
            "id": 1,
            "type": "USER",
            "msg": "Hi, can you provide your phone number for verification?"
        },
        {
            "id": 2,
            "type": "AGENT", 
            "msg": "Sure, my phone number is +1234567890"
        }
    ]
    
    print("Testing model with sample conversation...")
    result = pipeline.run(messages=test_messages, caseId="test_case")
    
    print("\nModel output structure:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    # Test the mapping function
    from evaluate_model_by_category import map_model_output_to_category
    
    review_res = result.get('review_res', {})
    predicted_category = map_model_output_to_category(review_res)
    
    print(f"\nMapped category: {predicted_category}")
    
    # Show which fields triggered the prediction
    print(f"\nDetailed analysis:")
    for key, value in review_res.items():
        if isinstance(value, dict) and value.get('hit_rule'):
            print(f"  {key}: HIT - {value}")
        elif isinstance(value, list) and value:
            print(f"  {key}: HIT - {len(value)} items")
        else:
            print(f"  {key}: no hit")

if __name__ == "__main__":
    test_model_output()
